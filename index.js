const express = require("express");
const jwt = require("jsonwebtoken");
const axios = require("axios");

const app = express();
app.use(express.json());

// Load service account credentials
const SERVICE_ACCOUNT = require("./service-account.json");

app.get("/token", async (req, res) => {
  try {
    const iat = Math.floor(Date.now() / 1000);
    const exp = iat + 60 * 60; // valid for 1 hour

    const payload = {
      iss: SERVICE_ACCOUNT.client_email,
      scope: "https://www.googleapis.com/auth/devstorage.read_write",
      aud: "https://oauth2.googleapis.com/token",
      exp,
      iat,
    };

    const token = jwt.sign(payload, SERVICE_ACCOUNT.private_key, {
      algorithm: "RS256",
    });

    const response = await axios.post("https://oauth2.googleapis.com/token", {
      grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
      assertion: token,
    });

    res.json({ access_token: response.data.access_token });
  } catch (err) {
    console.error("ERROR:", err.message);
    res.status(500).json({ error: err.message });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`Token server running on port ${PORT}`));
